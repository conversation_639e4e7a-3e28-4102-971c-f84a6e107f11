#!/bin/bash

# Oracle 11gR2 简化冷还原脚本
# 专门处理当前环境的还原需求

# 定义变量
BACKUP_DIR="/opt/oracle/rman_backup"
ORACLE_HOME="${ORACLE_HOME:-/opt/oracle/product/11gR2/db}"
ORACLE_SID="${ORACLE_SID:-PDBQZ}"
LOGFILE="$BACKUP_DIR/cold_recover_simple_$(date +%Y%m%d_%H%M%S).log"

# 设置环境变量
export ORACLE_HOME=$ORACLE_HOME
export ORACLE_SID=$ORACLE_SID
export PATH=$ORACLE_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

echo "[$(date)] === SIMPLIFIED RMAN COLD RESTORE START ===" | tee -a "$LOGFILE"
echo "[$(date)] ORACLE_HOME: $ORACLE_HOME" | tee -a "$LOGFILE"
echo "[$(date)] ORACLE_SID: $ORACLE_SID" | tee -a "$LOGFILE"
echo "[$(date)] BACKUP_DIR: $BACKUP_DIR" | tee -a "$LOGFILE"

# 创建必要目录
echo "[$(date)] Creating necessary directories..." | tee -a "$LOGFILE"
mkdir -p /opt/oracle/oradata/PDBQZ
mkdir -p /opt/oracle/flash_recovery_area/PDBQZ
mkdir -p /opt/oracle/admin/PDBQZ/adump
chown -R oracle:oinstall /opt/oracle/oradata/PDBQZ 2>/dev/null || true
chown -R oracle:oinstall /opt/oracle/flash_recovery_area/PDBQZ 2>/dev/null || true
chown -R oracle:oinstall /opt/oracle/admin/PDBQZ/adump 2>/dev/null || true

# 查找备份文件
echo "[$(date)] Looking for backup files..." | tee -a "$LOGFILE"
LATEST_DB_BACKUP=$(ls -t $BACKUP_DIR/coldbkp_*_*.bkp 2>/dev/null | head -1)
LATEST_CTL_BACKUP=$(ls -t $BACKUP_DIR/ctlfile_*.ctl 2>/dev/null | head -1)

if [ -z "$LATEST_DB_BACKUP" ] || [ -z "$LATEST_CTL_BACKUP" ]; then
  echo "[$(date)] ERROR: Backup files not found" | tee -a "$LOGFILE"
  echo "[$(date)] Looking for: coldbkp_*_*.bkp and ctlfile_*.ctl in $BACKUP_DIR" | tee -a "$LOGFILE"
  ls -la "$BACKUP_DIR" | tee -a "$LOGFILE"
  exit 1
fi

echo "[$(date)] Found database backup: $LATEST_DB_BACKUP" | tee -a "$LOGFILE"
echo "[$(date)] Found control file backup: $LATEST_CTL_BACKUP" | tee -a "$LOGFILE"

# 清理现有数据文件
echo "[$(date)] Cleaning existing data files..." | tee -a "$LOGFILE"
if [ -d "/opt/oracle/oradata/PDBQZ" ]; then
  rm -rf /opt/oracle/oradata/PDBQZ/* 2>/dev/null || true
fi

# 启动实例到NOMOUNT
echo "[$(date)] Starting instance to NOMOUNT..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SHUTDOWN ABORT;
STARTUP NOMOUNT;
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to start instance to NOMOUNT" | tee -a "$LOGFILE"
  exit 1
fi

# 步骤1：恢复控制文件
echo "[$(date)] Step 1: Restoring control file..." | tee -a "$LOGFILE"
rman target / <<EOF >> "$LOGFILE" 2>&1
RESTORE CONTROLFILE FROM '$LATEST_CTL_BACKUP';
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to restore control file" | tee -a "$LOGFILE"
  exit 1
fi

# 步骤2：挂载数据库
echo "[$(date)] Step 2: Mounting database..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
ALTER DATABASE MOUNT;
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to mount database" | tee -a "$LOGFILE"
  exit 1
fi

# 步骤3：编目备份文件
echo "[$(date)] Step 3: Cataloging backup files..." | tee -a "$LOGFILE"
rman target / <<EOF >> "$LOGFILE" 2>&1
CATALOG START WITH '$BACKUP_DIR' NOPROMPT;
EXIT;
EOF

# 步骤4：恢复数据库
echo "[$(date)] Step 4: Restoring database..." | tee -a "$LOGFILE"
rman target / <<EOF >> "$LOGFILE" 2>&1
RUN {
  RESTORE DATABASE;
  RECOVER DATABASE;
}
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to restore database" | tee -a "$LOGFILE"
  exit 1
fi

# 步骤5：打开数据库
echo "[$(date)] Step 5: Opening database..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
ALTER DATABASE OPEN RESETLOGS;
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to open database" | tee -a "$LOGFILE"
  exit 1
fi

# 验证数据库状态
echo "[$(date)] Verifying database status..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SELECT INSTANCE_NAME, STATUS FROM V\$INSTANCE;
SELECT NAME, OPEN_MODE FROM V\$DATABASE;
SELECT COUNT(*) AS "Data Files" FROM V\$DATAFILE;
SELECT COUNT(*) AS "User Tables" FROM DBA_TABLES WHERE OWNER NOT IN ('SYS','SYSTEM');
EXIT;
EOF

if [ $? -eq 0 ]; then
  echo "[$(date)] === DATABASE RESTORE COMPLETED SUCCESSFULLY ===" | tee -a "$LOGFILE"
  echo "[$(date)] Database is now open and ready for use" | tee -a "$LOGFILE"
  echo "[$(date)] Log file: $LOGFILE" | tee -a "$LOGFILE"
else
  echo "[$(date)] WARNING: Database opened but verification failed" | tee -a "$LOGFILE"
fi

echo "[$(date)] === SIMPLIFIED RMAN COLD RESTORE END ===" | tee -a "$LOGFILE"
