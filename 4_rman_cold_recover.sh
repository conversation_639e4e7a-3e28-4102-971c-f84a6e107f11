#!/bin/bash

# 定义变量
BACKUP_DIR="/opt/oracle/rman_backup"
ORACLE_HOME="/opt/oracle/product/11gR2/db"
ORACLE_SID="PDBQZ"
LOGFILE="/opt/oracle/rman_backup/cold_recover_$(date +%Y%m%d).log"

# 设置环境变量
export ORACLE_HOME=$ORACLE_HOME
export ORACLE_SID=$ORACLE_SID
export PATH=$ORACLE_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

# 日志记录
echo "[$(date)] === RMAN COLD RESTORE START ===" | tee -a "$LOGFILE"

# 检查 spfile
echo "[$(date)] Checking spfile..." | tee -a "$LOGFILE"
if [ ! -f "$ORACLE_HOME/dbs/spfile$ORACLE_SID.ora" ]; then
  echo "[$(date)] ERROR: spfile$ORACLE_SID.ora not found in $ORACLE_HOME/dbs" | tee -a "$LOGFILE"
  exit 1
fi

# 检查磁盘空间
echo "[$(date)] Checking disk space..." | tee -a "$LOGFILE"
df -h /opt/oracle/oradata >> "$LOGFILE"
if [ $(df -BG /opt/oracle/oradata | awk 'NR==2 {print $4}' | tr -d 'G') -lt 100 ]; then
  echo "[$(date)] ERROR: Insufficient disk space (<100GB available)." | tee -a "$LOGFILE"
  exit 1
fi

# 检查和创建必要目录
echo "[$(date)] Checking and creating directories..." | tee -a "$LOGFILE"
DIRS=(
  "/opt/oracle/oradata/PDBQZ"
  "/opt/oracle/flash_recovery_area/PDBQZ"
  "/opt/oracle/admin/PDBQZ/adump"
)
for DIR in "${DIRS[@]}"; do
  if [ ! -d "$DIR" ]; then
    mkdir -p "$DIR" || { echo "[$(date)] ERROR: Failed to create $DIR" | tee -a "$LOGFILE"; exit 1; }
    chown oracle:oinstall "$DIR"
    chmod 775 "$DIR"
    echo "[$(date)] Created $DIR" | tee -a "$LOGFILE"
  else
    chown oracle:oinstall "$DIR"
    chmod 775 "$DIR"
    echo "[$(date)] $DIR exists" | tee -a "$LOGFILE"
  fi
done

# 清理旧数据文件
echo "[$(date)] Cleaning old data files..." | tee -a "$LOGFILE"
mkdir -p /backup/oradata_backup
mv /opt/oracle/oradata/PDBQZ/* /backup/oradata_backup/ 2>/dev/null || true
echo "[$(date)] Old data files moved to /backup/oradata_backup" | tee -a "$LOGFILE"

# 检查备份文件
echo "[$(date)] Checking backup files..." | tee -a "$LOGFILE"
ls -l "$BACKUP_DIR" >> "$LOGFILE"
if [ ! -f "$BACKUP_DIR/coldbkp_PDBQZ_20250613_013rtrjg_1_1.bkp" ] || \
   [ ! -f "$BACKUP_DIR/coldbkp_PDBQZ_20250613_023rud7r_1_1.bkp" ] || \
   [ ! -f "$BACKUP_DIR/ctlfile_PDBQZ_20250613.ctl" ]; then
  echo "[$(date)] ERROR: Backup files missing in $BACKUP_DIR" | tee -a "$LOGFILE"
  exit 1
fi

# 启动实例到 NOMOUNT
echo "[$(date)] Starting instance to NOMOUNT..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
STARTUP NOMOUNT;
EXIT;
EOF
if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to start instance to NOMOUNT" | tee -a "$LOGFILE"
  exit 1
fi

# 验证备份并恢复
echo "[$(date)] Starting RMAN restore..." | tee -a "$LOGFILE"
rman target / log="$LOGFILE" <<EOF
RUN {
  # 验证备份
  RESTORE DATABASE VALIDATE;
  
  # 编目备份
  CATALOG START WITH '$BACKUP_DIR' NOPROMPT;
  
  # 恢复控制文件
  RESTORE CONTROLFILE FROM '$BACKUP_DIR/ctlfile_PDBQZ_20250613.ctl';
  ALTER DATABASE MOUNT;
  
  # 恢复数据库
  RESTORE DATABASE;
  RECOVER DATABASE;
  
  # 打开数据库
  ALTER DATABASE OPEN RESETLOGS;
}
# 检查数据文件
SQL "SELECT TABLESPACE_NAME, FILE_NAME FROM DBA_DATA_FILES";
EXIT;
EOF
if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: RMAN restore failed, check $LOGFILE for details" | tee -a "$LOGFILE"
  exit 1
fi

# 验证数据库连接
echo "[$(date)] Verifying database connection..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SELECT INSTANCE_NAME, STATUS FROM V\$INSTANCE;
EXIT;
EOF
if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Database connection verification failed" | tee -a "$LOGFILE"
  exit 1
fi

echo "[$(date)] === RMAN COLD RESTORE END ===" | tee -a "$LOGFILE"