#!/bin/bash

# 定义变量
BACKUP_DIR="/opt/oracle/rman_backup"
ORACLE_HOME="/opt/oracle/product/11gR2/db"
ORACLE_SID="PDBQZ"
LOGFILE="/opt/oracle/rman_backup/cold_recover_$(date +%Y%m%d).log"

# 设置环境变量
export ORACLE_HOME=$ORACLE_HOME
export ORACLE_SID=$ORACLE_SID
export PATH=$ORACLE_HOME/bin:$PATH
export LD_LIBRARY_PATH=$ORACLE_HOME/lib:$LD_LIBRARY_PATH

# 验证Oracle环境
echo "[$(date)] Verifying Oracle environment..." | tee -a "$LOGFILE"
if [ ! -d "$ORACLE_HOME" ]; then
  echo "[$(date)] ERROR: ORACLE_HOME directory does not exist: $ORACLE_HOME" | tee -a "$LOGFILE"
  exit 1
fi

if [ ! -f "$ORACLE_HOME/bin/sqlplus" ]; then
  echo "[$(date)] ERROR: sqlplus not found in $ORACLE_HOME/bin" | tee -a "$LOGFILE"
  exit 1
fi

if [ ! -f "$ORACLE_HOME/bin/rman" ]; then
  echo "[$(date)] ERROR: rman not found in $ORACLE_HOME/bin" | tee -a "$LOGFILE"
  exit 1
fi

echo "[$(date)] Oracle environment verified successfully" | tee -a "$LOGFILE"
echo "[$(date)] ORACLE_HOME: $ORACLE_HOME" | tee -a "$LOGFILE"
echo "[$(date)] ORACLE_SID: $ORACLE_SID" | tee -a "$LOGFILE"
echo "[$(date)] PATH: $PATH" | tee -a "$LOGFILE"

# 日志记录
echo "[$(date)] === RMAN COLD RESTORE START ===" | tee -a "$LOGFILE"

# 检查 spfile
echo "[$(date)] Checking spfile..." | tee -a "$LOGFILE"
if [ ! -f "$ORACLE_HOME/dbs/spfile$ORACLE_SID.ora" ]; then
  echo "[$(date)] ERROR: spfile$ORACLE_SID.ora not found in $ORACLE_HOME/dbs" | tee -a "$LOGFILE"
  exit 1
fi

# 检查磁盘空间
echo "[$(date)] Checking disk space..." | tee -a "$LOGFILE"
# 检查备份目录所在的磁盘空间
df -h "$BACKUP_DIR" >> "$LOGFILE" 2>&1
AVAILABLE_SPACE=$(df -BG "$BACKUP_DIR" 2>/dev/null | awk 'NR==2 {print $4}' | tr -d 'G')
if [ -n "$AVAILABLE_SPACE" ] && [ "$AVAILABLE_SPACE" -lt 10 ]; then
  echo "[$(date)] ERROR: Insufficient disk space (<10GB available)." | tee -a "$LOGFILE"
  exit 1
fi
echo "[$(date)] Disk space check completed" | tee -a "$LOGFILE"

# 检查和创建必要目录
echo "[$(date)] Checking and creating directories..." | tee -a "$LOGFILE"
DIRS=(
  "/opt/oracle/oradata/PDBQZ"
  "/opt/oracle/flash_recovery_area/PDBQZ"
  "/opt/oracle/admin/PDBQZ/adump"
)
for DIR in "${DIRS[@]}"; do
  if [ ! -d "$DIR" ]; then
    mkdir -p "$DIR" || { echo "[$(date)] ERROR: Failed to create $DIR" | tee -a "$LOGFILE"; exit 1; }
    chown oracle:oinstall "$DIR"
    chmod 775 "$DIR"
    echo "[$(date)] Created $DIR" | tee -a "$LOGFILE"
  else
    chown oracle:oinstall "$DIR"
    chmod 775 "$DIR"
    echo "[$(date)] $DIR exists" | tee -a "$LOGFILE"
  fi
done

# 清理旧数据文件
echo "[$(date)] Cleaning old data files..." | tee -a "$LOGFILE"
BACKUP_OLD_DIR="$BACKUP_DIR/old_datafiles_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_OLD_DIR" 2>/dev/null || {
  echo "[$(date)] Warning: Cannot create backup directory for old files" | tee -a "$LOGFILE"
  BACKUP_OLD_DIR="/tmp/oradata_backup_$(date +%Y%m%d_%H%M%S)"
  mkdir -p "$BACKUP_OLD_DIR"
}
if [ -d "/opt/oracle/oradata/PDBQZ" ] && [ "$(ls -A /opt/oracle/oradata/PDBQZ 2>/dev/null)" ]; then
  mv /opt/oracle/oradata/PDBQZ/* "$BACKUP_OLD_DIR/" 2>/dev/null || true
  echo "[$(date)] Old data files moved to $BACKUP_OLD_DIR" | tee -a "$LOGFILE"
else
  echo "[$(date)] No old data files to move" | tee -a "$LOGFILE"
fi

# 检查备份文件
echo "[$(date)] Checking backup files..." | tee -a "$LOGFILE"
ls -l "$BACKUP_DIR" >> "$LOGFILE"

# 动态查找最新的备份文件
LATEST_DB_BACKUP=$(ls -t $BACKUP_DIR/coldbkp_*_*.bkp 2>/dev/null | head -1)
LATEST_CTL_BACKUP=$(ls -t $BACKUP_DIR/ctlfile_*.ctl 2>/dev/null | head -1)

if [ -z "$LATEST_DB_BACKUP" ] || [ -z "$LATEST_CTL_BACKUP" ]; then
  echo "[$(date)] ERROR: No backup files found in $BACKUP_DIR" | tee -a "$LOGFILE"
  echo "[$(date)] Looking for: coldbkp_*_*.bkp and ctlfile_*.ctl" | tee -a "$LOGFILE"
  exit 1
fi

echo "[$(date)] Found database backup: $LATEST_DB_BACKUP" | tee -a "$LOGFILE"
echo "[$(date)] Found control file backup: $LATEST_CTL_BACKUP" | tee -a "$LOGFILE"

# 验证备份文件完整性
echo "[$(date)] Validating backup file integrity..." | tee -a "$LOGFILE"
if [ ! -r "$LATEST_DB_BACKUP" ]; then
  echo "[$(date)] ERROR: Cannot read database backup file: $LATEST_DB_BACKUP" | tee -a "$LOGFILE"
  exit 1
fi
if [ ! -r "$LATEST_CTL_BACKUP" ]; then
  echo "[$(date)] ERROR: Cannot read control file backup: $LATEST_CTL_BACKUP" | tee -a "$LOGFILE"
  exit 1
fi

# 启动实例到 NOMOUNT
echo "[$(date)] Starting instance to NOMOUNT..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SHUTDOWN ABORT;
STARTUP NOMOUNT;
EXIT;
EOF
if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to start instance to NOMOUNT" | tee -a "$LOGFILE"
  exit 1
fi

# 第一步：恢复控制文件并挂载数据库
echo "[$(date)] Step 1: Restoring controlfile and mounting database..." | tee -a "$LOGFILE"
rman target / log="$LOGFILE" <<EOF
RUN {
  # 编目备份
  CATALOG START WITH '$BACKUP_DIR' NOPROMPT;

  # 恢复控制文件
  RESTORE CONTROLFILE FROM '$LATEST_CTL_BACKUP';

  # 挂载数据库
  ALTER DATABASE MOUNT;
}
EXIT;
EOF

if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Failed to restore controlfile and mount database" | tee -a "$LOGFILE"
  exit 1
fi

# 第二步：验证备份并恢复数据库
echo "[$(date)] Step 2: Validating backup and restoring database..." | tee -a "$LOGFILE"
rman target / log="$LOGFILE" <<EOF
RUN {
  # 验证备份（现在数据库已经挂载）
  RESTORE DATABASE VALIDATE;

  # 恢复数据库
  RESTORE DATABASE;
  RECOVER DATABASE;

  # 打开数据库
  ALTER DATABASE OPEN RESETLOGS;
}
EXIT;
EOF
if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: RMAN restore failed, check $LOGFILE for details" | tee -a "$LOGFILE"
  exit 1
fi

# 验证数据库连接和检查数据文件
echo "[$(date)] Verifying database connection and checking data files..." | tee -a "$LOGFILE"
sqlplus -s / as sysdba <<EOF >> "$LOGFILE" 2>&1
SELECT INSTANCE_NAME, STATUS FROM V\$INSTANCE;
SELECT TABLESPACE_NAME, FILE_NAME FROM DBA_DATA_FILES;
SELECT COUNT(*) AS "Total Tables" FROM DBA_TABLES WHERE OWNER NOT IN ('SYS','SYSTEM');
EXIT;
EOF
if [ $? -ne 0 ]; then
  echo "[$(date)] ERROR: Database connection verification failed" | tee -a "$LOGFILE"
  exit 1
fi

echo "[$(date)] Database restore completed successfully!" | tee -a "$LOGFILE"

echo "[$(date)] === RMAN COLD RESTORE END ===" | tee -a "$LOGFILE"