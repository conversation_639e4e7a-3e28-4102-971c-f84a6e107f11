#!/bin/bash

BACKUP_DIR="/opt/oracle/rman_backup"
LOGFILE="$BACKUP_DIR/cold_backup_$(date +%Y%m%d).log"

mkdir -p "$BACKUP_DIR"

echo "[$(date)] === RMAN COLD BACKUP START ===" | tee -a "$LOGFILE"

# 启动数据库到 MOUNT 状态（关闭的数据库执行备份）
sqlplus -s / as sysdba <<EOF >> "$LOGFILE"
SHUTDOWN IMMEDIATE;
STARTUP MOUNT;
EXIT;
EOF

# 执行冷备份，包括控制文件和数据文件
rman target / log="$LOGFILE" <<EOF
RUN {
  CONFIGURE RETENTION POLICY TO REDUNDANCY 1;
  
  BACKUP AS COMPRESSED BACKUPSET DATABASE
    FORMAT '$BACKUP_DIR/coldbkp_%d_%T_%U.bkp'
    TAG='COLD_DB_BACKUP';

  BACKUP CURRENT CONTROLFILE
    FORMAT '$BACKUP_DIR/ctlfile_%d_%T.ctl';

  DELETE NOPROMPT OBSOLETE;
}
EOF

# 备份完成后关闭数据库
sqlplus -s / as sysdba <<EOF >> "$LOGFILE"
SHUTDOWN IMMEDIATE;
EXIT;
EOF

echo "[$(date)] === RMAN COLD BACKUP END ===" | tee -a "$LOGFILE"
